import { useState, useEffect } from "react";
import { FaPlay } from "react-icons/fa";
import { Icon } from "@iconify/react";
import InteractiveButton from "../components/InteractiveButton";
import { Link, useNavigate } from "react-router-dom";
import debounce from "lodash/debounce";
import { authService } from "../services/api";
import { useAuth } from "../hooks/useAuth";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";

export default function BecomeCreator() {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      pageName: "",
    },
    mode: "onChange",
  });
  const [isPlaying, setIsPlaying] = useState({
    video1: true,
    video2: true,
    video3: true,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCertified, setIsCertified] = useState(false);
  const navigate = useNavigate();
  const { user, isAuthenticated, updateProfile } = useAuth();
  const [pageNameError, setPageNameError] = useState("");
  const [isCheckingPageName, setIsCheckingPageName] = useState(false);
  const [pageNameSuccess, setPageNameSuccess] = useState(false);

  const handleCheckboxChange = () => {
    setIsCertified(!isCertified);
  };

  useEffect(() => {
    // Auto-play all videos when component mounts
    const videos = document.querySelectorAll("video");
    videos.forEach((video) => {
      video.play().catch((error) => {
        console.log("Auto-play was prevented:", error);
      });
    });
  }, []);

  const onSubmit = async (data) => {
    try {
      if (!data.pageName || data.pageName.length < 2) {
        setPageNameError("Page name must be at least 2 characters long");
        return;
      }
      // Allow submission if username is the same as current username
      if (!pageNameSuccess && data.pageName !== user?.username) {
        setPageNameError("Please choose a valid page name");
        return;
      }
      if (!isCertified) {
        toast.error("Please accept the terms of service");
        return;
      }
      setIsSubmitting(true);
      await updateProfile({
        isCreator: true,
        username: data.pageName,
      });
      toast.success("Successfully became a creator!");
      navigate("/creator");
    } catch (error) {
      console.error(error);
      toast.error(error?.response?.data?.message || "Something went wrong");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Debounced page name check
  const checkPageName = debounce(async (pageName) => {
    if (!pageName || pageName.length < 2) {
      setPageNameError("Page name must be at least 2 characters long");
      setPageNameSuccess(false);
      setIsCheckingPageName(false);
      return;
    }
    if (pageName === user?.username) {
      setPageNameError("");
      setPageNameSuccess(true); // Allow using the same username
      setIsCheckingPageName(false);
      return;
    }

    setIsCheckingPageName(true);
    setPageNameSuccess(false);
    try {
      const response = await authService.checkUsernamePublic(pageName);
      if (!response.data.available) {
        setPageNameError(response.data.message);
        setPageNameSuccess(false);
      } else {
        setPageNameError("");
        setPageNameSuccess(true);
      }
    } catch (error) {
      setPageNameError(
        error.response?.data?.message || "Error checking page name availability"
      );
      setPageNameSuccess(false);
    }
    setIsCheckingPageName(false);
  }, 500);

  // Watch username changes
  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name === "pageName") {
        checkPageName(value.pageName);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);
  //   console.log(user, isAuthenticated);
  return (
    <div className="flex flex-col items-center min-h-screen bg-gray-100">
      {/* Hero Section */}
      <div className="w-full relative h-full max-w-7xl px-4 py-10 md:py-20 flex  flex-col-reverse justify-between items-center md:flex-row gap-8">
        {/* Left Content */}
        <div className="w-full md:w-1/2 text-center md:text-left space-y-6 md:pr-8 flex-1">
          <span className="text-primary font-semibold text-base md:text-lg">
            FOR CREATORS
          </span>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-secondary leading-tight">
            Empowering you to do what you love
          </h1>
          <p className="text-gray-600 text-base md:text-lg leading-relaxed">
            Unleash your creativity, transform passion into profit! Join our
            creator community now and start your journey to content creation
            success. Let's talk big money!
          </p>
          <Link to={"#join"}>
            <button className="bg-primary text-white px-6 md:px-8 py-3 md:py-4 rounded-lg font-semibold text-base md:text-lg hover:bg-opacity-90 transition-all">
              Join Our Creator Community
            </button>
          </Link>
        </div>

        {/* Right Video Cards */}
        <div className="relative w-full md:w-1/2 flex flex-1 justify-center md:justify-end gap-2 overflow-x-hidden px-2">
          {/* First Card */}
          <div className="w-[150px] sm:w-[180px] md:w-[200px] bg-white h-fit rounded-lg shadow-xl overflow-hidden z-30">
            <div className="relative h-[250px] sm:h-[300px] md:h-[350px]">
              <video
                className="w-full h-full object-cover"
                loop
                muted
                playsInline
                autoPlay
                onMouseEnter={(e) => {
                  e.target.play();
                  setIsPlaying((prev) => ({ ...prev, video1: true }));
                }}
                onMouseLeave={(e) => {
                  e.target.pause();
                  setIsPlaying((prev) => ({ ...prev, video1: false }));
                }}
              >
                <source
                  src="https://videos.pexels.com/video-files/3403279/3403279-hd_1080_1920_25fps.mp4"
                  type="video/mp4"
                />
              </video>
              <div className="absolute inset-0 flex items-center justify-center bg-black/30 transition-opacity duration-300 hover:opacity-0">
                {!isPlaying.video1 && (
                  <FaPlay className="text-white text-4xl opacity-80" />
                )}
              </div>
              <div className="absolute top-4 right-4 bg-white px-3 py-1.5 md:px-4 md:py-2 rounded-full shadow-lg">
                <p className="text-lg md:text-xl font-bold">237K</p>
                <p className="text-xs md:text-sm text-gray-600">FOLLOWERS</p>
              </div>
            </div>
          </div>

          {/* Second Card */}
          <div className="w-[150px] sm:w-[180px] md:w-[200px] h-fit bg-white rounded-lg mt-10 md:mt-14 shadow-xl overflow-hidden z-20">
            <div className="relative h-[250px] sm:h-[300px] md:h-[350px]">
              <video
                className="w-full h-full object-cover"
                loop
                muted
                playsInline
                autoPlay
                onMouseEnter={(e) => {
                  e.target.play();
                  setIsPlaying((prev) => ({ ...prev, video2: true }));
                }}
                onMouseLeave={(e) => {
                  e.target.pause();
                  setIsPlaying((prev) => ({ ...prev, video2: false }));
                }}
              >
                <source
                  src="https://videos.pexels.com/video-files/3795508/3795508-uhd_1440_2732_25fps.mp4"
                  type="video/mp4"
                />
              </video>
              <div className="absolute inset-0 flex items-center justify-center bg-black/30 transition-opacity duration-300 hover:opacity-0">
                {!isPlaying.video2 && (
                  <FaPlay className="text-white text-4xl opacity-80" />
                )}
              </div>
              <div className="absolute top-4 right-4 bg-white px-3 py-1.5 md:px-4 md:py-2 rounded-full shadow-lg">
                <p className="text-lg md:text-xl font-bold">20K</p>
                <p className="text-xs md:text-sm text-gray-600">POSTS</p>
              </div>
            </div>
          </div>

          {/* Third Card */}
          <div className="w-[150px] sm:w-[180px] md:w-[200px] h-fit bg-white rounded-lg shadow-xl overflow-hidden z-10">
            <div className="relative h-[250px] sm:h-[300px] md:h-[350px]">
              <video
                className="w-full h-full object-cover"
                loop
                muted
                playsInline
                autoPlay
                onMouseEnter={(e) => {
                  e.target.play();
                  setIsPlaying((prev) => ({ ...prev, video3: true }));
                }}
                onMouseLeave={(e) => {
                  e.target.pause();
                  setIsPlaying((prev) => ({ ...prev, video3: false }));
                }}
              >
                <source
                  src="https://videos.pexels.com/video-files/3894693/3894693-uhd_1440_2732_25fps.mp4"
                  type="video/mp4"
                />
              </video>
              <div className="absolute inset-0 flex items-center justify-center bg-black/30 transition-opacity duration-300 hover:opacity-0">
                {!isPlaying.video3 && (
                  <FaPlay className="text-white text-4xl opacity-80" />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* How It Works Section */}
      <section className="w-full max-w-7xl px-4 py-10 md:py-20">
        <h2 className="text-2xl sm:text-4xl font-bold text-center text-secondary mb-16">
          How It Works
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-12">
          {/* What is Erossphere? */}
          <div className="space-y-4">
            <h3 className="text-2xl font-semibold text-secondary">
              What is Erossphere?
            </h3>
            <p className="text-gray-600 leading-relaxed">
              Erossphere is an innovative platform designed to help content
              creators maximize their earnings through exclusive, members-only
              social media profiles and a dedicated content sharing ecosystem.
              We bridge the gap between creative professionals and their devoted
              audience, offering a secure and engaging space for meaningful
              connections. Our user-friendly dashboard makes member management
              seamless and intuitive. With Erossphere, you'll have access to a
              comprehensive suite of features including Stories, Feed, Clips,
              private messaging, live streaming, and much more to help you build
              and monetize your community effectively. Join us today and
              transform your content creation journey!
            </p>
          </div>

          {/* How does Erossphere work for Creators? */}
          <div className="space-y-4">
            <h3 className="text-2xl font-semibold text-secondary">
              How does Erossphere work for Creators?
            </h3>
            <p className="text-gray-600 leading-relaxed">
              Starting your journey on Erossphere is quick and straightforward.
              Simply create your account on erossphere.com, customize your
              profile in minutes, and share your unique page link with your
              followers. That's all it takes to get started! Once your audience
              discovers your page, they can subscribe to various exclusive
              benefits and perks you offer. Subscribers gain privileged access
              to your exclusive content and personal interactions, maintaining
              their access through active subscriptions. Plus, you can boost
              your earnings by participating in our referral program or joining
              our affiliate network.
            </p>
          </div>

          {/* How does Erossphere handle payouts? */}
          <div className="space-y-4">
            <h3 className="text-2xl font-semibold text-secondary">
              How does Erossphere handle payouts?
            </h3>
            <p className="text-gray-600 leading-relaxed">
              We process creator payments weekly through multiple secure payment
              methods including bank transfers, digital wallets, and
              cryptocurrency options, ensuring you get paid reliably and on
              time. Our automated payment system handles all transactions
              securely, with detailed reporting and tracking available through
              your creator dashboard.
            </p>
            <p className="text-gray-600 leading-relaxed">
              *We're proud to offer an industry-leading 80% revenue share for
              all new subscribers starting from October 1st, 2021! For
              subscribers who joined before Aug 20, 2021, the payout rate
              remains at 75%.
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="w-full max-w-7xl px-4 py-20 bg-white">
        <h2 className="text-2xl sm:text-4xl font-bold text-center text-secondary mb-16">
          Features
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Row 1 */}
          <div className="flex flex-col items-center text-center space-y-4">
            <Icon
              icon="solar:rocket-2-outline"
              className="w-12 h-12 text-secondary"
            />
            <h3 className="text-lg font-semibold text-secondary">
              Migrate Your Content
            </h3>
            <p className="text-gray-600">
              Transfer all your existing content to Erossphere without losing a
              single follower.
            </p>
          </div>

          <div className="flex flex-col items-center text-center space-y-4">
            <Icon
              icon="solar:wallet-money-outline"
              className="w-12 h-12 text-secondary"
            />
            <h3 className="text-lg font-semibold text-secondary">
              80% Earnings
            </h3>
            <p className="text-gray-600">
              Earn 80% on every single purchase made by your subscribers*
            </p>
          </div>

          <div className="flex flex-col items-center text-center space-y-4">
            <Icon
              icon="solar:refresh-circle-outline"
              className="w-12 h-12 text-secondary"
            />
            <h3 className="text-lg font-semibold text-secondary">
              Referral Bonus
            </h3>
            <p className="text-gray-600">
              Earn 10% of Erossphere's revenue share on every referred creator!
            </p>
          </div>

          <div className="flex flex-col items-center text-center space-y-4">
            <Icon
              icon="solar:chat-square-like-outline"
              className="w-12 h-12 text-secondary"
            />
            <h3 className="text-lg font-semibold text-secondary">
              Direct Messages
            </h3>
            <p className="text-gray-600">
              Send DMs with locked or unlocked content to your paid subscribers
              AND your free followers.
            </p>
          </div>

          {/* Row 3 */}
          <div className="flex flex-col items-center text-center space-y-4">
            <Icon
              icon="solar:videocamera-record-outline"
              className="w-12 h-12 text-secondary"
            />
            <h3 className="text-lg font-semibold text-secondary">HD Content</h3>
            <p className="text-gray-600">
              Post clips and videos right on your Erossphere profile, and in
              your personal clip store!
            </p>
          </div>

          <div className="flex flex-col items-center text-center space-y-4">
            <Icon
              icon="solar:play-stream-line-duotone"
              className="w-12 h-12 text-secondary"
            />
            <h3 className="text-lg font-semibold text-secondary">
              Live Streaming
            </h3>
            <p className="text-gray-600">
              Go live for your followers and subscribers!
            </p>
          </div>

          <div className="flex flex-col items-center text-center space-y-4">
            <Icon
              icon="streamline:live-video"
              className="w-12 h-12 text-secondary"
            />
            <h3 className="text-lg font-semibold text-secondary">
              24-hour Stories
            </h3>
            <p className="text-gray-600">
              The best way to boost both your followers and subscribers is with
              24-hour Stories!
            </p>
          </div>

          <div className="flex flex-col items-center text-center space-y-4">
            <Icon
              icon="solar:star-outline"
              className="w-12 h-12 text-secondary"
            />
            <h3 className="text-lg font-semibold text-secondary">
              Get Discovered
            </h3>
            <p className="text-gray-600">
              Get discovered on our Directory, set up a custom domain and so
              much more....
            </p>
          </div>
        </div>
      </section>

      {/* Sign Up Section */}
      <section className="w-full max-w-7xl px-4 py-10 md:py-20" id="join">
        <div className="max-w-lg mx-auto">
          <h2 className="text-2xl sm:text-4xl font-bold text-center text-secondary mb-16">
            Sign Up as Creator
          </h2>

          {isAuthenticated && !user?.isCreator && (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Page Name Input */}
              <div>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 -translate-y-1/2 flex items-center pointer-events-none">
                    <Icon
                      icon="solar:user-outline"
                      className="w-6 h-6 text-gray-400"
                    />
                  </div>
                  <input
                    type="text"
                    placeholder="Page name"
                    name="pageName"
                    defaultValue={user?.username || user?.displayName || ""}
                    {...register("pageName", {
                      required: "Page name is required",
                      minLength: {
                        value: 2,
                        message: "Page name must be at least 2 characters long",
                      },
                      pattern: {
                        value: /^[a-zA-Z0-9_-]+$/,
                        message:
                          "Page name can only contain letters, numbers, underscores and dashes",
                      },
                    })}
                    className={`w-full pl-12 pr-${
                      isCheckingPageName ? "12" : "4"
                    } py-4 border ${
                      pageNameError || errors.pageName
                        ? "border-red-500"
                        : pageNameSuccess
                        ? "border-green-500"
                        : "border-gray-300"
                    } rounded-lg focus:outline-none focus:ring-2 ${
                      pageNameError || errors.pageName
                        ? "focus:ring-red-500"
                        : pageNameSuccess
                        ? "focus:ring-green-500"
                        : "focus:ring-primary"
                    } disabled:bg-gray-100 disabled:text-gray-400 transition-all duration-200`}
                  />
                  {isCheckingPageName && (
                    <div className="absolute right-3 top-1/2 -translate-y-1/2">
                      <Icon
                        icon="eos-icons:loading"
                        className="w-6 h-6 text-gray-400 animate-spin"
                      />
                    </div>
                  )}
                  {pageNameSuccess && !pageNameError && !isCheckingPageName && (
                    <div className="absolute right-3 top-1/2 -translate-y-1/2">
                      <Icon
                        icon="solar:check-circle-bold"
                        className="w-6 h-6 text-green-500"
                      />
                    </div>
                  )}
                </div>
                {pageNameError && (
                  <p className="mt-1 text-sm text-red-500">{pageNameError}</p>
                )}
                {pageNameSuccess && !pageNameError && !isCheckingPageName && (
                  <p className="mt-1 text-sm text-green-500">
                    Page name is available!
                  </p>
                )}
              </div>

              {/* URL Display */}
              <div className="text-center bg-gray-50 p-3 rounded-lg border border-gray-200">
                <p className="text-gray-500 font-medium">
                  Your page URL will be:
                </p>
                <p className="text-primary font-semibold">
                  https://erossphere.com/{watch("pageName") || "yourpagename"}
                </p>
              </div>

              {/* Terms Checkbox */}
              <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                <input
                  checked={isCertified}
                  onChange={handleCheckboxChange}
                  type="checkbox"
                  id="terms"
                  className="mt-1.5 h-5 w-5 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <label htmlFor="terms" className="text-gray-600 text-sm">
                  I certify that I am at least 18-years old, have the capacity
                  to enter into legally binding contracts, and have read and
                  agree to the{" "}
                  <a
                    href="/terms"
                    className="text-primary font-medium hover:underline"
                  >
                    Terms of Service
                  </a>
                </label>
              </div>

              {/* Submit Button */}
              <InteractiveButton
                isLoading={isSubmitting}
                type="submit"
                disabled={
                  isSubmitting ||
                  !isCertified ||
                  (!pageNameSuccess && watch("pageName") !== user?.username)
                }
                className={`w-full py-4 px-6 bg-primary text-white rounded-lg font-semibold text-lg shadow-lg transition-all ${
                  isSubmitting ||
                  !isCertified ||
                  (!pageNameSuccess && watch("pageName") !== user?.username)
                    ? "opacity-50 cursor-not-allowed"
                    : "hover:bg-opacity-90 hover:shadow-xl transform hover:-translate-y-0.5"
                }`}
              >
                {isSubmitting ? "Processing..." : "Become a Creator"}
              </InteractiveButton>
            </form>
          )}
          {!isAuthenticated && <BecomeCreatorSignupForm />}
        </div>
      </section>
    </div>
  );
}

const BecomeCreatorSignupForm = () => {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      pageName: "",
      email: "",
      password: "",
    },
    mode: "onChange",
  });
  const [isCheckingPageName, setIsCheckingPageName] = useState(false);
  const [pageNameError, setPageNameError] = useState("");
  const [pageNameSuccess, setPageNameSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCertified, setIsCertified] = useState(false);
  const navigate = useNavigate();
  const { register: registerUser } = useAuth();

  const onSubmit = async (data) => {
    try {
      if (!data.pageName || data.pageName.length < 2) {
        setPageNameError("Page name must be at least 2 characters long");
        return;
      }
      if (!pageNameSuccess) {
        setPageNameError("Please choose a valid page name");
        return;
      }
      if (!isCertified) {
        toast.error("Please accept the terms of service");
        return;
      }
      setIsSubmitting(true);
      await registerUser({
        email: data.email,
        password: data.password,
        username: data.pageName,
        isCreator: true,
      });
      toast.success("Registration successful");
      navigate("/creator");
    } catch (error) {
      console.error(error);
      if (error?.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Something went wrong");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Debounced page name check
  const checkPageName = debounce(async (pageName) => {
    if (!pageName || pageName.length < 2) {
      setPageNameError("Page name must be at least 2 characters long");
      setPageNameSuccess(false);
      setIsCheckingPageName(false);
      return;
    }

    setIsCheckingPageName(true);
    setPageNameSuccess(false);
    try {
      const response = await authService.checkUsernamePublic(pageName);
      if (!response.data.available) {
        setPageNameError(response.data.message);
        setPageNameSuccess(false);
      } else {
        setPageNameError("");
        setPageNameSuccess(true);
      }
    } catch (error) {
      setPageNameError(
        error.response?.data?.message || "Error checking page name availability"
      );
      setPageNameSuccess(false);
    }
    setIsCheckingPageName(false);
  }, 500);

  // Watch username changes
  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name === "pageName") {
        checkPageName(value.pageName);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);
  return (
    <div>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Email Input */}
        <div>
          <div className="relative">
            <div className="absolute left-3 top-1/2 -translate-y-1/2 flex items-center pointer-events-none">
              <Icon
                icon="solar:mail-outline"
                className="w-6 h-6 text-gray-400"
              />
            </div>
            <input
              type="email"
              placeholder="Email"
              name="email"
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address",
                },
              })}
              className={`w-full pl-12 pr-4 py-4 border ${
                errors.email ? "border-red-500" : "border-gray-300"
              } rounded-lg focus:outline-none focus:ring-2 ${
                errors.email ? "focus:ring-red-500" : "focus:ring-primary"
              } transition-all duration-200`}
            />
            {errors.email && !errors.email.message && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <Icon
                  icon="solar:check-circle-bold"
                  className="w-6 h-6 text-green-500"
                />
              </div>
            )}
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>
          )}
        </div>
        {/* Page Name Input */}
        <div>
          <div className="relative">
            <div className="absolute left-3 top-1/2 -translate-y-1/2 flex items-center pointer-events-none">
              <Icon
                icon="solar:user-outline"
                className="w-6 h-6 text-gray-400"
              />
            </div>
            <input
              type="text"
              placeholder="Page name"
              name="pageName"
              {...register("pageName", {
                required: "Page name is required",
                minLength: {
                  value: 2,
                  message: "Page name must be at least 2 characters long",
                },
                pattern: {
                  value: /^[a-zA-Z0-9_-]+$/,
                  message:
                    "Page name can only contain letters, numbers, underscores and dashes",
                },
              })}
              className={`w-full pl-12 pr-${
                isCheckingPageName ? "12" : "4"
              } py-4 border ${
                pageNameError || errors.pageName
                  ? "border-red-500"
                  : pageNameSuccess
                  ? "border-green-500"
                  : "border-gray-300"
              } rounded-lg focus:outline-none focus:ring-2 ${
                pageNameError || errors.pageName
                  ? "focus:ring-red-500"
                  : pageNameSuccess
                  ? "focus:ring-green-500"
                  : "focus:ring-primary"
              } disabled:bg-gray-100 disabled:text-gray-400 transition-all duration-200`}
            />
            {isCheckingPageName && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <Icon
                  icon="eos-icons:loading"
                  className="w-6 h-6 text-gray-400 animate-spin"
                />
              </div>
            )}
            {pageNameSuccess && !pageNameError && !isCheckingPageName && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <Icon
                  icon="solar:check-circle-bold"
                  className="w-6 h-6 text-green-500"
                />
              </div>
            )}
          </div>
          {pageNameError && (
            <p className="mt-1 text-sm text-red-500">{pageNameError}</p>
          )}
          {pageNameSuccess && !pageNameError && !isCheckingPageName && (
            <p className="mt-1 text-sm text-green-500">
              Page name is available!
            </p>
          )}
        </div>
        {/* Password Input */}
        <div>
          <div className="relative">
            <div className="absolute left-3 top-1/2 -translate-y-1/2 flex items-center pointer-events-none">
              <Icon
                icon="solar:lock-outline"
                className="w-6 h-6 text-gray-400"
              />
            </div>
            <input
              type="password"
              placeholder="Password"
              name="password"
              {...register("password", {
                required: "Password is required",
                minLength: {
                  value: 6,
                  message: "Password must be at least 6 characters long",
                },
              })}
              className={`w-full pl-12 pr-4 py-4 border ${
                errors.password ? "border-red-500" : "border-gray-300"
              } rounded-lg focus:outline-none focus:ring-2 ${
                errors.password ? "focus:ring-red-500" : "focus:ring-primary"
              } transition-all duration-200`}
            />
            {!errors.password && watch("password")?.length >= 6 && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <Icon
                  icon="solar:check-circle-bold"
                  className="w-6 h-6 text-green-500"
                />
              </div>
            )}
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-500">
              {errors.password.message}
            </p>
          )}
        </div>
        {/* URL Display */}
        <div className="text-center bg-gray-50 p-3 rounded-lg border border-gray-200">
          <p className="text-gray-500 font-medium">Your page URL will be:</p>
          <p className="text-primary font-semibold">
            https://erossphere.com/{watch("pageName") || "yourpagename"}
          </p>
        </div>
        {/* Terms Checkbox */}
        <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
          <input
            checked={isCertified}
            onChange={() => setIsCertified(!isCertified)}
            type="checkbox"
            id="terms-signup"
            className="mt-1.5 h-5 w-5 rounded border-gray-300 text-primary focus:ring-primary"
          />
          <label htmlFor="terms-signup" className="text-gray-600 text-sm">
            I certify that I am at least 18-years old, have the capacity to
            enter into legally binding contracts, and have read and agree to the{" "}
            <a
              href="/terms"
              className="text-primary font-medium hover:underline"
            >
              Terms of Service
            </a>
          </label>
        </div>
        {/* Submit Button */}
        <InteractiveButton
          isLoading={isSubmitting}
          type="submit"
          disabled={
            isSubmitting ||
            !isCertified ||
            !pageNameSuccess ||
            Object.keys(errors).length > 0
          }
          className={`w-full py-4 px-6 bg-primary text-white rounded-lg font-semibold text-lg shadow-lg transition-all ${
            isSubmitting ||
            !isCertified ||
            !pageNameSuccess ||
            Object.keys(errors).length > 0
              ? "opacity-50 cursor-not-allowed"
              : "hover:bg-opacity-90 hover:shadow-xl transform hover:-translate-y-0.5"
          }`}
        >
          {isSubmitting ? "Processing..." : "Become a Creator"}
        </InteractiveButton>
      </form>
    </div>
  );
};
