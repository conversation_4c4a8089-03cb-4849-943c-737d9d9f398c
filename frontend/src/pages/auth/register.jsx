import { Link, useNavi<PERSON> } from "react-router-dom";
import { useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useAuth } from "../../hooks/useAuth";
import toast from "react-hot-toast";
import AuthSocial from "../../components/auth/AuthSocial";
import InteractiveButton from "../../components/InteractiveButton";
import { FiMail } from "react-icons/fi";
import { authService } from "../../services/api";
import UserTypeSelection from "../../components/auth/UserTypeSelection";
import { Icon } from "@iconify/react";
// Add validation schema
const schema = yup.object().shape({
  displayName: yup
    .string()
    .required("Display name is required")
    .min(3, "Display name must be at least 3 characters"),
  email: yup
    .string()
    .required("Email is required")
    .email("Must be a valid email"),
  password: yup
    .string()
    .required("Password is required")
    .min(8, "Password must be at least 8 characters")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Password must contain at least one uppercase letter, one lowercase letter, and one number"
    ),
  confirmPassword: yup
    .string()
    .required("Please confirm your password")
    .oneOf([yup.ref("password")], "Passwords must match"),
});

const verificationSchema = yup.object().shape({
  verificationCode: yup
    .string()
    .required("Verification code is required")
    .matches(/^\d{6}$/, "Code must be 6 digits"),
});

export default function Register() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { register: registerUser, verifyEmail } = useAuth();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [registeredEmail, setRegisteredEmail] = useState("");
  const [currentStep, setCurrentStep] = useState(1); // 1: user type, 2: form
  const [selectedUserType, setSelectedUserType] = useState(null);
  const navigate = useNavigate();
  // Update useForm to use yup resolver
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const {
    register: registerVerification,
    handleSubmit: handleVerificationSubmit,
    formState: { errors: verificationErrors },
  } = useForm({
    resolver: yupResolver(verificationSchema),
  });

  const onSubmit = async (data) => {
    setSubmitLoading(true);

    try {
      // Add user type to registration data
      const registrationData = {
        ...data,
        userType: selectedUserType,
        isCreator: selectedUserType === "creator",
      };

      await registerUser(registrationData).then((response) => {
        console.log(response, "response");
        setRegisteredEmail(data.email);
        toast.success("Registration successful");
        setShowVerificationModal(true);
      });
    } catch (error) {
      console.log(error);
      if (error?.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Something went wrong");
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  const handleUserTypeSelect = (type) => {
    setSelectedUserType(type);
    if (type === "creator") {
      // Redirect to become creator page for full setup
      navigate("/sell");
    } else {
      // Continue with regular registration for fans
      setCurrentStep(2);
    }
  };

  const onVerificationSubmit = async (data) => {
    setVerificationLoading(true);

    try {
      const response = await authService.verifyEmail(
        registeredEmail,
        data.verificationCode
      );
      toast.success("Email verified successfully");

      navigate("/login", {
        state: {
          verifiedEmail: registeredEmail,
          message: "Your email has been verified. You can now log in.",
        },
      });
    } catch (error) {
      console.error("Verification error:", error);
      if (error?.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Verification failed. Please try again.");
      }
    } finally {
      setVerificationLoading(false);
    }
  };

  const handleResendCode = async () => {
    setResendLoading(true);

    try {
      await authService.resendVerificationCode(registeredEmail);
      toast.success("New verification code sent");
    } catch (error) {
      if (error?.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Something went wrong");
      }
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <div className="w-full lg:w-1/2 p-8 flex items-center justify-center">
      <div className="w-full max-w-md space-y-8">
        {/* Step 1: User Type Selection */}
        {currentStep === 1 && (
          <>
            <div className="text-center">
              <h1 className="text-4xl font-medium mb-2">Join Erossphere</h1>
              <p className="text-gray-600 mb-8">
                Choose how you'd like to get started
              </p>
            </div>

            <div className="space-y-4">
              {/* Fan Option */}
              <button
                onClick={() => handleUserTypeSelect("fan")}
                className="w-full p-6 border-2 border-gray-200 rounded-2xl hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 text-left group"
              >
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Icon
                      icon="solar:heart-line-duotone"
                      className="w-6 h-6 text-white"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1 group-hover:text-blue-600">
                      Join as a Fan
                    </h3>
                    <p className="text-gray-600 text-sm mb-3">
                      Discover and support amazing creators
                    </p>
                    <div className="flex items-center gap-2">
                      <span className="bg-blue-100 text-blue-700 text-xs font-medium px-2 py-1 rounded-full">
                        Free to join
                      </span>
                      <Icon
                        icon="solar:arrow-right-linear"
                        className="w-4 h-4 text-gray-400 group-hover:text-blue-500"
                      />
                    </div>
                  </div>
                </div>
              </button>

              {/* Creator Option */}
              <button
                onClick={() => handleUserTypeSelect("creator")}
                className="w-full p-6 border-2 border-gray-200 rounded-2xl hover:border-purple-500 hover:bg-purple-50 transition-all duration-200 text-left group"
              >
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Icon
                      icon="solar:star-line-duotone"
                      className="w-6 h-6 text-white"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1 group-hover:text-purple-600">
                      Become a Creator
                    </h3>
                    <p className="text-gray-600 text-sm mb-3">
                      Monetize your content and build your community
                    </p>
                    <div className="flex items-center gap-2">
                      <span className="bg-gradient-to-r from-purple-500 to-pink-600 text-white text-xs font-medium px-2 py-1 rounded-full">
                        Start earning
                      </span>
                      <Icon
                        icon="solar:arrow-right-linear"
                        className="w-4 h-4 text-gray-400 group-hover:text-purple-500"
                      />
                    </div>
                  </div>
                </div>
              </button>
            </div>

            <div className="text-center">
              <p className="text-gray-600 text-sm">
                Already have an account?{" "}
                <Link to="/login" className="text-primary hover:underline">
                  Log in
                </Link>
              </p>
            </div>
          </>
        )}

        {/* Step 2: Registration Form (for fans) */}
        {currentStep === 2 && (
          <>
            <div className="flex items-center gap-3 mb-6">
              <button
                onClick={() => setCurrentStep(1)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Icon
                  icon="solar:arrow-left-linear"
                  className="w-5 h-5 text-gray-600"
                />
              </button>
              <div>
                <h1 className="text-4xl font-medium mb-2">
                  Create your account
                </h1>
                <p className="text-gray-600">
                  Join as a fan • Already have an account?{" "}
                  <Link to="/login" className="text-primary hover:underline">
                    Log in
                  </Link>
                </p>
              </div>
            </div>

            <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-2">
                <input
                  type="text"
                  placeholder="Display name"
                  className={`w-full p-3 rounded-lg bg-gray-100 border ${
                    errors.displayName ? "border-red-500" : "border-gray-200"
                  } focus:outline-none focus:ring-2 focus:ring-primary-500`}
                  {...register("displayName")}
                />
                {errors.displayName && (
                  <p className="text-red-500 text-sm">
                    {errors.displayName.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <input
                  type="email"
                  placeholder="Email"
                  className={`w-full p-3 rounded-lg bg-gray-100 border ${
                    errors.email ? "border-red-500" : "border-gray-200"
                  } focus:outline-none focus:ring-2 focus:ring-primary-500`}
                  {...register("email")}
                />
                {errors.email && (
                  <p className="text-red-500 text-sm">{errors.email.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    className={`w-full p-3 rounded-lg bg-gray-100 border ${
                      errors.password ? "border-red-500" : "border-gray-200"
                    } focus:outline-none focus:ring-2 focus:ring-primary-500`}
                    {...register("password")}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2"
                  >
                    {showPassword ? "👁️" : "👁️‍🗨️"}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-red-500 text-sm">
                    {errors.password.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <div className="relative">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm your password"
                    className={`w-full p-3 rounded-lg bg-gray-100 border ${
                      errors.confirmPassword
                        ? "border-red-500"
                        : "border-gray-200"
                    } focus:outline-none focus:ring-2 focus:ring-primary-500`}
                    {...register("confirmPassword")}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2"
                  >
                    {showConfirmPassword ? "👁️" : "👁️‍🗨️"}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-red-500 text-sm">
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>

              <div className="flex items-center gap-2">
                <p className="text-sm text-gray-600">
                  By signing up, you agree to our{" "}
                  <Link to="/terms" className="text-primary hover:underline">
                    Terms of Service
                  </Link>{" "}
                  and{" "}
                  <Link to="/privacy" className="text-primary hover:underline">
                    Privacy Policy
                  </Link>{" "}
                  and confirm that you're at least 18 years old.
                </p>
              </div>

              <InteractiveButton
                className="w-full"
                type="submit"
                variant="primary"
                isLoading={submitLoading}
              >
                Create account
              </InteractiveButton>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">
                    Or register with
                  </span>
                </div>
              </div>

              <AuthSocial />
            </form>
          </>
        )}
      </div>

      {/* Verification Email Modal */}
      {showVerificationModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                <FiMail className="w-8 h-8 text-primary" />
              </div>
              <h2 className="text-2xl font-semibold">Check your email</h2>
              <p className="text-gray-600">
                We've sent a verification code to your email address. Please
                enter the code below to verify your account.
              </p>

              <form
                onSubmit={handleVerificationSubmit(onVerificationSubmit)}
                className="w-full space-y-4"
              >
                <div className="space-y-2">
                  <input
                    type="text"
                    placeholder="Enter 6-digit code"
                    className="w-full p-3 rounded-lg bg-gray-100 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 text-center text-2xl tracking-widest"
                    maxLength="6"
                    {...registerVerification("verificationCode")}
                  />
                  {verificationErrors.verificationCode && (
                    <p className="text-red-500 text-sm">
                      {verificationErrors.verificationCode.message}
                    </p>
                  )}
                </div>

                <div className="flex flex-col gap-3 w-full">
                  <InteractiveButton
                    className="w-full"
                    type="submit"
                    variant="primary"
                    isLoading={verificationLoading}
                  >
                    Verify Email
                  </InteractiveButton>

                  <button
                    type="button"
                    className="text-primary hover:underline text-sm"
                    onClick={handleResendCode}
                    disabled={resendLoading}
                  >
                    {resendLoading ? "Sending..." : "Resend Code"}
                  </button>

                  <button
                    type="button"
                    className="text-gray-500 hover:text-gray-700 text-sm"
                    onClick={() => setShowVerificationModal(false)}
                  >
                    Close
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
