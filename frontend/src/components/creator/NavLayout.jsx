import { Link, useLocation } from "react-router-dom";
import { Icon } from "@iconify/react";
import { useAuth } from "../../hooks/useAuth";
import InteractiveButton from "../InteractiveButton";
import PropTypes from "prop-types";
import useNotificationStore, {
  useNotificationsQuery,
} from "../../store/notificationStore";
import { useEffect, useState } from "react";
import Avatar from "../Avatar";
import Skeleton from "../ui/Skeleton";
import Modal from "../Modal";
import { useNavigate } from "react-router-dom";
import { useConversations } from "../../services/chatQueries";
import { useSocket } from "../../hooks/useSocket";

export default function NavLayout() {
  const location = useLocation();
  const { user, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Use the new notification query
  const { data: notificationData, isLoading: notificationsLoading } =
    useNotificationsQuery(1, { status: "all" });
  const totalUnread = notificationData?.totalUnread || 0;

  // Use TanStack Query hooks
  const { data: chatData } = useConversations();

  // Set up socket connection
  useSocket(user?._id);

  const navigate = useNavigate();
  const navItems = [
    {
      name: "Feed",
      icon: <Icon icon="solar:home-angle-broken" fontSize={30} />,
      path: "/",
      showOnMobile: true,
    },
    {
      name: "Notifications",
      icon: <Icon fontSize={30} icon="solar:bell-line-duotone" />,
      path: "/notifications",
      showOnMobile: true,
    },
    {
      name: "Chats",
      icon: <Icon fontSize={30} icon="solar:chat-dots-line-duotone" />,
      path: "/chats",
      showOnMobile: true,
    },
    // {
    //   name: "Collections",
    //   icon: <Icon fontSize={30} icon="fluent:collections-30-regular" />,
    //   path: "/collections",
    //   showOnMobile: true,
    // },
    {
      name: "Subscriptions",
      icon: <Icon fontSize={30} icon="solar:user-plus-linear" />,
      path: "/subscriptions",
      showOnMobile: false,
    },
    {
      name: user?.isCreator ? "Creator dashboard" : "Become a creator",
      icon: <Icon fontSize={30} icon="solar:hand-money-line-duotone" />,
      path: user?.isCreator ? "/creator" : "/sell",
      showOnMobile: false,
    },
    {
      name: "Wallet",
      icon: <Icon icon="solar:wallet-broken" fontSize={30} />,
      path: "/wallet",
      showOnMobile: false,
    },
    {
      name: "My profile",
      icon: <Icon fontSize={30} icon="solar:user-outline" />,
      path: "/profile",
      showOnMobile: false,
    },
    {
      name: "Settings",
      icon: <Icon fontSize={30} icon="solar:settings-minimalistic-outline" />,
      path: "/settings",
      showOnMobile: false,
    },
    {
      name: "More",
      icon: <Icon fontSize={30} icon="ri:more-fill" />,
      path: "/more",
      showOnMobile: true,
    },
    // {
    //   name: "Add post",
    //   icon: <Icon fontSize={30} icon="basil:add-outline" />,
    //   path: "/add-post",
    //   showOnMobile: false,
    // },
    {
      name: "Logout",
      icon: <Icon fontSize={30} icon="solar:logout-linear" />,
      path: "/logout",
      showOnMobile: false,
    },
  ];
  // console.log({ unreadCounts, totalUnreadCount });

  if (notificationsLoading) {
    return (
      <>
        {/* Desktop and Tablet Skeleton */}
        <div className="hidden sm:block">
          <div className="w-72 lg:w-72 sm:w-16 flex-shrink-0 sticky top-5">
            <div className="bg-white rounded-2xl p-4">
              {/* User Profile Section Skeleton */}
              <div className="hidden lg:block">
                <div className="flex items-center gap-3 mb-4">
                  <Skeleton variant="avatar" />
                  <div className="flex-1">
                    <Skeleton variant="title" className="mb-2" />
                    <Skeleton variant="text" width="100px" />
                  </div>
                </div>

                <div className="flex justify-between text-sm mb-6">
                  {[...Array(3)].map((_, index) => (
                    <div key={index} className="flex flex-col gap-1">
                      <Skeleton variant="text" width="40px" className="mb-1" />
                      <Skeleton variant="text" width="60px" />
                    </div>
                  ))}
                </div>
              </div>

              {/* Navigation Items Skeleton */}
              <nav className="space-y-2">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="flex items-center gap-3 p-2">
                    <Skeleton variant="avatar" width="30px" height="30px" />
                    <Skeleton
                      variant="text"
                      className="hidden lg:block"
                      width="120px"
                    />
                  </div>
                ))}
              </nav>

              {/* Add Post Button Skeleton */}
              {/* <div className="mt-5">
                <Skeleton variant="button" className="w-full" height="40px" />
              </div> */}
            </div>
          </div>
        </div>

        {/* Mobile Bottom Navigation Skeleton */}
        <div className="sm:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 z-50">
          <nav className="flex justify-around py-2">
            {[...Array(4)].map((_, index) => (
              <Skeleton
                key={index}
                variant="avatar"
                width="30px"
                height="30px"
              />
            ))}
          </nav>
        </div>
      </>
    );
  }

  return (
    <>
      {/* Desktop and Tablet View */}
      <div className="hidden sm:block">
        <div className="w-72 lg:w-72 sm:w-16 flex-shrink-0 sticky top-5">
          <div className="bg-white rounded-2xl p-4">
            {/* User Profile Section - Hide on small tablet */}
            <div className="hidden lg:block">
              <div className="flex items-center gap-3 mb-4">
                <Avatar avatar={user?.avatar} />
                <div>
                  <h3 className="font-semibold">
                    {user?.displayName || user?.username}
                  </h3>
                  <p className="text-gray-500 text-sm">@{user?.username}</p>
                </div>
              </div>
            </div>

            {/* Navigation Items */}
            <nav className="space-y-2">
              {navItems
                .filter((item) => !item.showOnMobile || item.name !== "More")
                .map((item, index) => (
                  <Link
                    to={item.path}
                    className={`flex items-center gap-3 p-2 relative rounded-lg transition-colors ${
                      location.pathname === item.path
                        ? "bg-blue-50 text-primary"
                        : "text-gray-600 hover:bg-gray-50"
                    }`}
                    key={index}
                    onClick={(e) => {
                      if (item.path === "/logout") {
                        e.preventDefault();
                        logout();
                      }
                    }}
                  >
                    <div className="flex items-center gap-3">
                      {item.icon}
                      <span className="hidden lg:inline">{item.name}</span>
                    </div>
                    {item.path === "/notifications" && totalUnread > 0 && (
                      <div className="!w-5 h-5 absolute top-[2px] right-[-10px] bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                        {totalUnread}
                      </div>
                    )}
                    {item.path === "/chats" &&
                      chatData?.totalUnreadCount > 0 && (
                        <div className="!w-5 h-5 absolute top-[2px] right-[-10px] bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                          {chatData.totalUnreadCount}
                        </div>
                      )}
                  </Link>
                ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="sm:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 z-50">
        <nav className="flex justify-around py-2">
          {navItems
            .filter((item) => item.showOnMobile)
            .map((item, index) => (
              <Link
                to={item.path}
                className={`p-2 rounded-lg transition-colors relative ${
                  location.pathname === item.path
                    ? "text-primary"
                    : "text-gray-600"
                }`}
                key={index}
                onClick={(e) => {
                  if (item.name === "More") {
                    e.preventDefault();
                    setIsMobileMenuOpen(true);
                  }
                }}
              >
                {item.icon}
                {item.path === "/notifications" && totalUnread > 0 && (
                  <div className="!w-5 h-5 absolute top-[2px] right-1 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                    {totalUnread}
                  </div>
                )}
                {item.path === "/chats" && chatData?.totalUnreadCount > 0 && (
                  <div className="!w-5 h-5 absolute top-[2px] right-1 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                    {chatData.totalUnreadCount}
                  </div>
                )}
              </Link>
            ))}
        </nav>
      </div>

      {/* Mobile Menu Modal */}
      <Modal
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        // title="Menu"
        className="sm:hidden"
      >
        <div className="flex flex-col space-y-2">
          {/* User Profile Section */}
          <div className="flex items-center gap-3 p-4 border-b border-gray-100">
            <Avatar avatar={user?.avatar} />
            <div>
              <h3 className="font-semibold">
                {user?.displayName || user?.username}
              </h3>
              <p className="text-gray-500 text-sm">@{user?.username}</p>
            </div>
          </div>

          {/* Stats Section */}
          <div className="flex justify-between text-sm text-gray-500 p-4 border-b border-gray-100">
            <div>
              <div className="font-semibold text-black">
                {user?.subscriberCount}
              </div>
              Subscribers
            </div>
            <div>
              <div className="font-semibold text-black">
                {user?.followingCount}
              </div>
              Following
            </div>
            <div>
              <div className="font-semibold text-black">{user?.totalPosts}</div>
              Post
            </div>
          </div>

          {/* Navigation Items */}
          <div className="p-2">
            {navItems
              .filter(
                (item) => item.path !== "/more" && item.path !== "/add-post"
              )
              .map((item, index) => (
                <Link
                  to={item.path}
                  className={`flex relative items-center gap-3 p-3 rounded-lg transition-colors ${
                    location.pathname === item.path
                      ? "bg-blue-50 text-primary"
                      : "text-gray-600 hover:bg-gray-50"
                  }`}
                  key={index}
                  onClick={(e) => {
                    if (item.path === "/logout") {
                      e.preventDefault();
                      logout();
                    }
                    setIsMobileMenuOpen(false);
                  }}
                >
                  {item.icon}
                  <span>{item.name}</span>
                  {item.path === "/notifications" && totalUnread > 0 && (
                    <div className="ml-auto w-5 h-5 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                      {totalUnread}
                    </div>
                  )}
                  {item.path === "/chats" && chatData?.totalUnreadCount > 0 && (
                    <div className="ml-auto w-5 h-5 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                      {chatData.totalUnreadCount}
                    </div>
                  )}
                </Link>
              ))}
          </div>

          {/* Add Post Button */}
          {/* <div className="p-4">
            <InteractiveButton
              onClick={() => {
                setIsMobileMenuOpen(false);
                navigate("/add-post");
              }}
              className="w-full"
            >
              Add post
            </InteractiveButton>
          </div> */}
        </div>
      </Modal>
    </>
  );
}

// Add PropTypes validation
// NavLayout.propTypes = {
//   addPostClick: PropTypes.func.isRequired,
// };
