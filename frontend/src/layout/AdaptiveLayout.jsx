import { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Icon } from "@iconify/react";
import { useAuth } from "../hooks/useAuth";
import { useLayoutMode } from "../hooks/useLayoutMode";
import { useNotificationsQuery } from "../store/notificationStore";
import { useConversations } from "../services/chatQueries";
import { useSocket } from "../hooks/useSocket";
import Avatar from "../components/Avatar";
import Skeleton from "../components/ui/Skeleton";
import Modal from "../components/Modal";
import InteractiveButton from "../components/InteractiveButton";
import { FaPlus, FaPen, FaVideo } from "react-icons/fa";
import { BsCameraVideo } from "react-icons/bs";

// Navigation configuration for different user types
const getNavigationConfig = (user, isCreatorMode) => {
  const baseNavItems = [
    {
      name: "Feed",
      icon: <Icon icon="solar:home-angle-broken" fontSize={30} />,
      path: "/",
      showOnMobile: true,
      roles: ["fan", "creator"],
    },
    {
      name: "Notifications",
      icon: <Icon fontSize={30} icon="solar:bell-line-duotone" />,
      path: isCreatorMode ? "/creator/notifications" : "/notifications",
      showOnMobile: true,
      roles: ["fan", "creator"],
    },
    {
      name: "Messages",
      icon: <Icon fontSize={30} icon="solar:chat-dots-line-duotone" />,
      path: isCreatorMode ? "/creator/messages" : "/chats",
      showOnMobile: true,
      roles: ["fan", "creator"],
    },
  ];

  const fanNavItems = [
    {
      name: "Subscriptions",
      icon: <Icon fontSize={30} icon="solar:user-plus-linear" />,
      path: "/subscriptions",
      showOnMobile: false,
      roles: ["fan"],
    },
    {
      name: "Wallet",
      icon: <Icon icon="solar:wallet-broken" fontSize={30} />,
      path: "/wallet",
      showOnMobile: false,
      roles: ["fan"],
    },
  ];

  const creatorNavItems = [
    {
      name: "Dashboard",
      icon: <Icon icon="solar:chart-square-line-duotone" fontSize={30} />,
      path: "/creator",
      showOnMobile: false,
      roles: ["creator"],
    },
    {
      name: "Add post",
      icon: <Icon fontSize={30} icon="basil:add-outline" />,
      path: "/creator/add-post",
      showOnMobile: false,
      roles: ["creator"],
    },
    {
      name: "Posts",
      icon: (
        <Icon fontSize={30} icon="solar:posts-carousel-vertical-line-duotone" />
      ),
      path: "/creator/posts",
      showOnMobile: false,
      roles: ["creator"],
    },
    {
      name: "Stories",
      icon: <Icon fontSize={30} icon="iconoir:lens-plus" />,
      path: "/creator/stories",
      showOnMobile: false,
      roles: ["creator"],
    },
    {
      name: "Subscribers",
      icon: <Icon fontSize={30} icon="solar:user-plus-linear" />,
      path: "/creator/subscribers",
      showOnMobile: false,
      roles: ["creator"],
    },
    {
      name: "Earnings",
      icon: <Icon fontSize={30} icon="solar:wallet-broken" />,
      path: "/creator/transactions",
      showOnMobile: false,
      roles: ["creator"],
    },
    {
      name: "Manage subscription",
      icon: <Icon fontSize={30} icon="solar:card-2-linear" />,
      path: "/creator/manage-subscription",
      showOnMobile: false,
      roles: ["creator"],
    },
  ];

  const commonNavItems = [
    {
      name: user?.isCreator ? "Switch to Fan Mode" : "Become a Creator",
      icon: <Icon fontSize={30} icon="solar:hand-money-line-duotone" />,
      path: user?.isCreator ? (isCreatorMode ? "/" : "/creator") : "/sell",
      showOnMobile: false,
      roles: ["fan", "creator"],
      isSwitch: user?.isCreator,
    },
    {
      name: "My profile",
      icon: <Icon fontSize={30} icon="solar:user-outline" />,
      path: "/profile",
      showOnMobile: false,
      roles: ["fan", "creator"],
    },
    {
      name: "Settings",
      icon: <Icon fontSize={30} icon="solar:settings-minimalistic-outline" />,
      path: isCreatorMode ? "/creator/account" : "/settings",
      showOnMobile: false,
      roles: ["fan", "creator"],
    },
    {
      name: "More",
      icon: <Icon fontSize={30} icon="ri:more-fill" />,
      path: "/more",
      showOnMobile: true,
      roles: ["fan", "creator"],
    },
    {
      name: "Logout",
      icon: <Icon fontSize={30} icon="solar:logout-linear" />,
      path: "/logout",
      showOnMobile: false,
      roles: ["fan", "creator"],
    },
  ];

  const currentRole = isCreatorMode ? "creator" : "fan";
  const roleSpecificItems = isCreatorMode ? creatorNavItems : fanNavItems;

  return [...baseNavItems, ...roleSpecificItems, ...commonNavItems].filter(
    (item) => item.roles.includes(currentRole)
  );
};

export default function AdaptiveLayout() {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showAddContent, setShowAddContent] = useState(false);

  // Use the layout mode hook for intelligent mode management
  const {
    currentMode,
    isCreatorMode,
    isFanMode,
    isTransitioning,
    canSwitchToCreator,
    switchMode,
    getNavigationConfig: getNavConfig,
    getModeStyles,
  } = useLayoutMode();

  // Get navigation items and styles based on current mode
  const navItems = getNavConfig();
  const modeStyles = getModeStyles();

  // Notification and chat data
  const { data: notificationData, isLoading: notificationsLoading } =
    useNotificationsQuery(1, { status: "all" });
  const totalUnread = notificationData?.totalUnread || 0;

  const { data: chatData } = useConversations();
  const totalUnreadCount = chatData?.totalUnreadCount || 0;

  // Set up socket connection
  useSocket(user?._id);

  const handleNavigation = (item, e) => {
    if (item.path === "/logout") {
      e.preventDefault();
      logout();
      return;
    }

    if (item.isSwitch) {
      e.preventDefault();
      const targetMode = isCreatorMode ? "fan" : "creator";
      switchMode(targetMode, { preserveRoute: true });
      return;
    }

    if (item.name === "More") {
      e.preventDefault();
      setIsMobileMenuOpen(true);
      return;
    }
  };

  if (notificationsLoading) {
    return <LayoutSkeleton />;
  }

  return (
    <>
      {/* Desktop and Tablet View */}
      <div className="hidden sm:block">
        <div className="w-72 lg:w-72 sm:w-16 flex-shrink-0 sticky top-5">
          <div
            className={`rounded-2xl p-4 ${modeStyles.sidebarBg} ${
              isTransitioning
                ? "opacity-75 transition-opacity duration-300"
                : ""
            }`}
          >
            {/* Mode Indicator */}
            {user?.isCreator && (
              <div className="hidden lg:block mb-4">
                <div
                  className={`text-xs font-medium px-3 py-1 rounded-full text-center ${
                    isCreatorMode
                      ? "bg-primary text-white"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  {isCreatorMode ? "Creator Mode" : "Fan Mode"}
                </div>
              </div>
            )}

            {/* User Profile Section */}
            <div className="hidden lg:block">
              <div className="flex items-center gap-3 mb-4">
                <Avatar avatar={user?.avatar} />
                <div>
                  <h3 className="font-semibold">
                    {user?.displayName || user?.username}
                  </h3>
                  <p className="text-gray-500 text-sm">@{user?.username}</p>
                </div>
              </div>

              {/* Stats Section */}
              <div className="flex justify-between text-sm text-gray-500 mb-6">
                <div>
                  <div className="font-semibold text-black">
                    {user?.subscriberCount || 0}
                  </div>
                  {isCreatorMode ? "Subscribers" : "Subscriptions"}
                </div>
                <div>
                  <div className="font-semibold text-black">
                    {user?.followingCount || 0}
                  </div>
                  Following
                </div>
                <div>
                  <div className="font-semibold text-black">
                    {user?.totalPosts || 0}
                  </div>
                  Posts
                </div>
              </div>
            </div>

            {/* Navigation Items */}
            <nav className="space-y-2">
              {navItems
                .filter((item) => !item.showOnMobile || item.name !== "More")
                .map((item, index) => (
                  <Link
                    to={item.path}
                    className={`flex items-center gap-3 p-2 relative rounded-lg transition-colors ${
                      location.pathname === item.path
                        ? "bg-blue-50 text-primary"
                        : "text-gray-600 hover:bg-gray-50"
                    } ${
                      item.isSwitch
                        ? "border border-gray-200 hover:border-primary"
                        : ""
                    }`}
                    key={index}
                    onClick={(e) => handleNavigation(item, e)}
                  >
                    <div className="flex items-center gap-3">
                      {item.icon}
                      <span className="hidden lg:inline">{item.name}</span>
                    </div>
                    {item.path.includes("/notifications") &&
                      totalUnread > 0 && (
                        <div className="!w-5 h-5 absolute top-[2px] right-[-10px] bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                          {totalUnread}
                        </div>
                      )}
                    {item.path.includes("/messages") &&
                      totalUnreadCount > 0 && (
                        <div className="!w-5 h-5 absolute top-[2px] right-[-10px] bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                          {totalUnreadCount}
                        </div>
                      )}
                  </Link>
                ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="sm:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 z-50">
        <nav className="flex justify-around py-2">
          {navItems
            .filter((item) => item.showOnMobile)
            .map((item, index) => (
              <Link
                to={item.path}
                className={`p-2 rounded-lg transition-colors relative ${
                  location.pathname === item.path
                    ? "text-primary"
                    : "text-gray-600"
                }`}
                key={index}
                onClick={(e) => handleNavigation(item, e)}
              >
                {item.icon}
                {item.path.includes("/notifications") && totalUnread > 0 && (
                  <div className="!w-5 h-5 absolute top-[2px] right-1 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                    {totalUnread}
                  </div>
                )}
                {item.path.includes("/messages") && totalUnreadCount > 0 && (
                  <div className="!w-5 h-5 absolute top-[2px] right-1 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                    {totalUnreadCount}
                  </div>
                )}
              </Link>
            ))}
        </nav>
      </div>

      {/* Creator Floating Action Button */}
      {isCreatorMode && (
        <CreatorFloatingActions
          showAddContent={showAddContent}
          setShowAddContent={setShowAddContent}
          navigate={navigate}
        />
      )}

      {/* Mobile Menu Modal */}
      <MobileMenuModal
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        navItems={navItems}
        user={user}
        location={location}
        handleNavigation={handleNavigation}
        totalUnread={totalUnread}
        totalUnreadCount={totalUnreadCount}
        isCreatorMode={isCreatorMode}
      />
    </>
  );
}

// Creator Floating Actions Component
function CreatorFloatingActions({
  showAddContent,
  setShowAddContent,
  navigate,
}) {
  return (
    <div className="fixed bottom-20 md:bottom-8 right-8 z-50">
      <div className="relative">
        <button
          onClick={() => setShowAddContent(!showAddContent)}
          className="bg-primary hover:bg-primary-400 text-white px-6 py-3 rounded-full font-medium flex items-center gap-2 shadow-lg transition-all duration-200"
        >
          <FaPlus className="w-5 h-5" />
          <span className="hidden md:block">Add content</span>
        </button>

        {/* Dropdown Menu */}
        {showAddContent && (
          <>
            <div className="absolute bottom-full right-0 mb-2 w-64 bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
              <div className="p-2 space-y-1">
                <button
                  onClick={() => {
                    navigate("/creator/add-post");
                    setShowAddContent(false);
                  }}
                  className="w-full flex items-center gap-3 px-4 py-3 text-left text-secondary hover:bg-gray-50 rounded-xl transition-colors duration-200"
                >
                  <FaPen className="w-5 h-5 text-primary" />
                  <span className="font-medium">Add new post</span>
                </button>
                <button
                  onClick={() => {
                    navigate("/creator/stories");
                    setShowAddContent(false);
                  }}
                  className="w-full flex items-center gap-3 px-4 py-3 text-left text-secondary hover:bg-gray-50 rounded-xl transition-colors duration-200"
                >
                  <FaPlus className="w-5 h-5 text-primary" />
                  <span className="font-medium">Add to story</span>
                </button>
                <button className="w-full flex items-center gap-3 px-4 py-3 text-left text-secondary hover:bg-gray-50 rounded-xl transition-colors duration-200">
                  <FaVideo className="w-5 h-5 text-primary" />
                  <span className="font-medium">Add new clip</span>
                </button>
                <button
                  onClick={() => {
                    navigate("/creator/live-stream");
                    setShowAddContent(false);
                  }}
                  className="w-full flex items-center gap-3 px-4 py-3 text-left text-secondary hover:bg-gray-50 rounded-xl transition-colors duration-200"
                >
                  <BsCameraVideo className="w-5 h-5 text-primary" />
                  <span className="font-medium">Go live</span>
                </button>
              </div>
            </div>
            {/* Click Away Layer */}
            <div
              className="fixed inset-0 z-40"
              onClick={() => setShowAddContent(false)}
            ></div>
          </>
        )}
      </div>
    </div>
  );
}

// Mobile Menu Modal Component
function MobileMenuModal({
  isOpen,
  onClose,
  navItems,
  user,
  location,
  handleNavigation,
  totalUnread,
  totalUnreadCount,
  isCreatorMode,
}) {
  return (
    <Modal isOpen={isOpen} onClose={onClose} className="sm:hidden">
      <div className="flex flex-col space-y-2">
        {/* User Profile Section */}
        <div className="flex items-center gap-3 p-4 border-b border-gray-100">
          <Avatar avatar={user?.avatar} />
          <div className="flex-1">
            <h3 className="font-semibold">
              {user?.displayName || user?.username}
            </h3>
            <p className="text-gray-500 text-sm">@{user?.username}</p>
            {user?.isCreator && (
              <div
                className={`text-xs font-medium px-2 py-1 rounded-full mt-1 inline-block ${
                  isCreatorMode
                    ? "bg-primary text-white"
                    : "bg-gray-200 text-gray-600"
                }`}
              >
                {isCreatorMode ? "Creator Mode" : "Fan Mode"}
              </div>
            )}
          </div>
        </div>

        {/* Stats Section */}
        <div className="flex justify-between text-sm text-gray-500 p-4 border-b border-gray-100">
          <div>
            <div className="font-semibold text-black">
              {user?.subscriberCount || 0}
            </div>
            {isCreatorMode ? "Subscribers" : "Subscriptions"}
          </div>
          <div>
            <div className="font-semibold text-black">
              {user?.followingCount || 0}
            </div>
            Following
          </div>
          <div>
            <div className="font-semibold text-black">
              {user?.totalPosts || 0}
            </div>
            Posts
          </div>
        </div>

        {/* Navigation Items */}
        <div className="p-2">
          {navItems
            .filter((item) => item.path !== "/more")
            .map((item, index) => (
              <Link
                to={item.path}
                className={`flex relative items-center gap-3 p-3 rounded-lg transition-colors ${
                  location.pathname === item.path
                    ? "bg-blue-50 text-primary"
                    : "text-gray-600 hover:bg-gray-50"
                } ${
                  item.isSwitch
                    ? "border border-gray-200 hover:border-primary"
                    : ""
                }`}
                key={index}
                onClick={(e) => {
                  handleNavigation(item, e);
                  onClose();
                }}
              >
                {item.icon}
                <span>{item.name}</span>
                {item.path.includes("/notifications") && totalUnread > 0 && (
                  <div className="ml-auto w-5 h-5 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                    {totalUnread}
                  </div>
                )}
                {item.path.includes("/messages") && totalUnreadCount > 0 && (
                  <div className="ml-auto w-5 h-5 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                    {totalUnreadCount}
                  </div>
                )}
              </Link>
            ))}
        </div>

        {/* Add Post Button for Creators */}
        {isCreatorMode && (
          <div className="p-4">
            <InteractiveButton
              onClick={() => {
                onClose();
                navigate("/creator/add-post");
              }}
              className="w-full"
            >
              Add post
            </InteractiveButton>
          </div>
        )}
      </div>
    </Modal>
  );
}

// Loading Skeleton Component
function LayoutSkeleton() {
  return (
    <>
      {/* Desktop and Tablet Skeleton */}
      <div className="hidden sm:block">
        <div className="w-72 lg:w-72 sm:w-16 flex-shrink-0 sticky top-5">
          <div className="bg-white rounded-2xl p-4">
            {/* User Profile Section Skeleton */}
            <div className="hidden lg:block">
              <div className="flex items-center gap-3 mb-4">
                <Skeleton variant="avatar" />
                <div className="flex-1">
                  <Skeleton variant="title" className="mb-2" />
                  <Skeleton variant="text" width="100px" />
                </div>
              </div>

              <div className="flex justify-between text-sm mb-6">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="flex flex-col gap-1">
                    <Skeleton variant="text" width="40px" className="mb-1" />
                    <Skeleton variant="text" width="60px" />
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation Items Skeleton */}
            <nav className="space-y-2">
              {[...Array(8)].map((_, index) => (
                <div key={index} className="flex items-center gap-3 p-2">
                  <Skeleton variant="avatar" width="30px" height="30px" />
                  <Skeleton
                    variant="text"
                    className="hidden lg:block"
                    width="120px"
                  />
                </div>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navigation Skeleton */}
      <div className="sm:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 z-50">
        <nav className="flex justify-around py-2">
          {[...Array(4)].map((_, index) => (
            <Skeleton key={index} variant="avatar" width="30px" height="30px" />
          ))}
        </nav>
      </div>
    </>
  );
}
